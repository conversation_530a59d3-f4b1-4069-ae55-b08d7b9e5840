DROP POLICY IF EXISTS "Give read only access to admin users" ON threads;

CREATE POLICY "Give read only access to admin users" ON threads
FOR SELECT
USING (
    ((auth.jwt() ->> 'email'::text) IN ('<EMAIL>', '<EMAIL>'))
);


DROP POLICY IF EXISTS "Give read only access to admin users" ON messages;

CREATE POLICY "Give read only access to admin users" ON messages
FOR SELECT
USING (
    ((auth.jwt() ->> 'email'::text) IN ('<EMAIL>', '<EMAIL>'))
);


DROP POLICY IF EXISTS "Give read only access to admin users" ON projects;

CREATE POLICY "Give read only access to admin users" ON projects
FOR SELECT
USING (
    ((auth.jwt() ->> 'email'::text) IN ('<EMAIL>', '<EMAIL>'))
);
